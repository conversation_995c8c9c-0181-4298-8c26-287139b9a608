<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="796181eb-c794-4d5c-b32e-49a3472d8120" name="Changes" comment="初始化">
      <change afterPath="$PROJECT_DIR$/crawlers/cfsa_crawler.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/crawlers/foodmate_crawler.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/db_schema_update.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/models/data_general.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/10_new_food_magazine.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/11_food_additives_part_a.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/12_food_additives_part_b.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/13_food_safety_news.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/14_wf_med.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/4_foodmate.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/5_cfsa.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/6_cfs_hk.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/7_food_alerts_news.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/7_food_alerts_news.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/8_mpi_govt.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/9_chemosphere.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/chromedriver.exe" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/temp/test1.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/templates/batch_details/general_table.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/crawlers/base_crawler.py" beforeDir="false" afterPath="$PROJECT_DIR$/crawlers/base_crawler.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/routes/batches.py" beforeDir="false" afterPath="$PROJECT_DIR$/routes/batches.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/routes/index.py" beforeDir="false" afterPath="$PROJECT_DIR$/routes/index.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/routes/tasks.py" beforeDir="false" afterPath="$PROJECT_DIR$/routes/tasks.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/capture_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/services/capture_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/static/js/batch_detail.js" beforeDir="false" afterPath="$PROJECT_DIR$/static/js/batch_detail.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/temp/3_wf_magazine.py" beforeDir="false" afterPath="$PROJECT_DIR$/temp/3_wf_magazine.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/batch_detail.html" beforeDir="false" afterPath="$PROJECT_DIR$/templates/batch_detail.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templates/batch_list.html" beforeDir="false" afterPath="$PROJECT_DIR$/templates/batch_list.html" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2zz9NQ1ezERcniVvT5TGnB50ZeV" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.10_new_food_magazine.executor": "Run",
    "Python.11_food_additives_part_a.executor": "Run",
    "Python.11_food_additives_part_b.executor": "Run",
    "Python.12_food_additives_part_b.executor": "Run",
    "Python.13_food_safety_news.executor": "Run",
    "Python.14_wf_med.executor": "Run",
    "Python.1_fda.executor": "Run",
    "Python.3_wf_magazine.executor": "Run",
    "Python.4_foodmate.executor": "Run",
    "Python.5_cfsa.executor": "Run",
    "Python.6_cfs_hk.executor": "Run",
    "Python.7_food_alerts_news.executor": "Run",
    "Python.8_mpi_govt.executor": "Run",
    "Python.9_chemosphere.executor": "Run",
    "Python.app.executor": "Run",
    "Python.cfsa_crawler.executor": "Run",
    "Python.config.executor": "Run",
    "Python.tencent_translate.executor": "Run",
    "Python.test.executor": "Run",
    "Python.test1.executor": "Run",
    "Python.test1_clean.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "F:/huimen_work/DataCapture/temp",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "configurable.group.appearance",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="F:\huimen_work\DataCapture\temp" />
      <recent name="F:\huimen_work\DataCapture" />
      <recent name="F:\huimen_work\DataCapture\utils" />
    </key>
  </component>
  <component name="RunManager" selected="Python.10_new_food_magazine">
    <configuration name="10_new_food_magazine" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="DataCapture" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/temp" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/temp/10_new_food_magazine.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="11_food_additives_part_a" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="DataCapture" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/temp" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/temp/11_food_additives_part_a.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="12_food_additives_part_b" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="DataCapture" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/temp" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/temp/12_food_additives_part_b.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="13_food_safety_news" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="DataCapture" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/temp" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/temp/13_food_safety_news.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="9_chemosphere" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="DataCapture" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/temp" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/temp/9_chemosphere.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.10_new_food_magazine" />
        <item itemvalue="Python.13_food_safety_news" />
        <item itemvalue="Python.12_food_additives_part_b" />
        <item itemvalue="Python.11_food_additives_part_a" />
        <item itemvalue="Python.9_chemosphere" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-deb605915726-JavaScript-PY-243.22562.180" />
        <option value="bundled-python-sdk-85c76a3b01b3-9a18a617cbe4-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.22562.180" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="796181eb-c794-4d5c-b32e-49a3472d8120" name="Changes" comment="" />
      <created>1752722993482</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752722993482</updated>
      <workItem from="1752722994543" duration="4879000" />
      <workItem from="1752731848131" duration="1618000" />
      <workItem from="1752733585546" duration="11132000" />
      <workItem from="1752801510749" duration="21092000" />
      <workItem from="1752828292723" duration="1554000" />
      <workItem from="1752997282323" duration="17000" />
      <workItem from="1753060778390" duration="15930000" />
      <workItem from="1753235319392" duration="15016000" />
      <workItem from="1753335717272" duration="12000" />
      <workItem from="1755489130678" duration="10727000" />
      <workItem from="1755568090827" duration="19575000" />
      <workItem from="1755651833089" duration="18616000" />
      <workItem from="1755738413217" duration="21070000" />
    </task>
    <task id="LOCAL-00001" summary="初始化">
      <option name="closed" value="true" />
      <created>1755500653679</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755500653679</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="初始化" />
    <option name="LAST_COMMIT_MESSAGE" value="初始化" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/DataCapture$9_chemosphere.coverage" NAME="9_chemosphere Coverage Results" MODIFIED="1755762408599" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$10_new_food_magazine.coverage" NAME="10_new_food_magazine Coverage Results" MODIFIED="1755763341651" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$4_foodmate.coverage" NAME="4_foodmate Coverage Results" MODIFIED="1755744074688" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$config.coverage" NAME="config Coverage Results" MODIFIED="1752741647425" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/DataCapture$11_food_additives_part_b.coverage" NAME="11_food_additives_part_b Coverage Results" MODIFIED="1755653503568" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$14_wf_med.coverage" NAME="14_wf_med Coverage Results" MODIFIED="1755682050725" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$tencent_translate.coverage" NAME="tencent_translate Coverage Results" MODIFIED="1752821904354" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/utils" />
    <SUITE FILE_PATH="coverage/DataCapture$1_fda.coverage" NAME="1_fda Coverage Results" MODIFIED="1752743152385" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$6_cfs_hk.coverage" NAME="6_cfs_hk Coverage Results" MODIFIED="1755754713782" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$test1_clean.coverage" NAME="test1_clean Coverage Results" MODIFIED="1755662976939" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$test.coverage" NAME="test Coverage Results" MODIFIED="1755596357074" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$app.coverage" NAME="app Coverage Results" MODIFIED="1755755253807" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/DataCapture$test1.coverage" NAME="test1 Coverage Results" MODIFIED="1755662627967" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$3_wf_magazine.coverage" NAME="3_wf_magazine Coverage Results" MODIFIED="1755758793012" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$11_food_additives_part_a.coverage" NAME="11_food_additives_part_a Coverage Results" MODIFIED="1755762623111" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$13_food_safety_news.coverage" NAME="13_food_safety_news Coverage Results" MODIFIED="1755762822313" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$12_food_additives_part_b.coverage" NAME="12_food_additives_part_b Coverage Results" MODIFIED="1755762798518" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$5_cfsa.coverage" NAME="5_cfsa Coverage Results" MODIFIED="1755750402312" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$8_mpi_govt.coverage" NAME="8_mpi_govt Coverage Results" MODIFIED="1755759914918" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$7_food_alerts_news.coverage" NAME="7_food_alerts_news Coverage Results" MODIFIED="1755756961393" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/temp" />
    <SUITE FILE_PATH="coverage/DataCapture$cfsa_crawler.coverage" NAME="cfsa_crawler Coverage Results" MODIFIED="1755750277593" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/crawlers" />
  </component>
</project>