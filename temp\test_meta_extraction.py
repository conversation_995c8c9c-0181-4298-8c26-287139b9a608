#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试作者和日期提取功能
"""

from bs4 import BeautifulSoup

def test_meta_extraction():
    # 测试HTML结构
    html_content = '''
    <article>
        <h3><a href="/some-article">Test Article Title</a></h3>
        <p class="meta">20 August 2025 | By <a href="https://www.newfoodmagazine.com/content_author/pete-gillett/" rel="tag"><PERSON></a></p>
        <p class="listOnly">This is the article excerpt...</p>
    </article>
    '''
    
    soup = BeautifulSoup(html_content, 'html.parser')
    article_element = soup.find('article')
    
    # 提取数据的逻辑（从修改后的代码复制）
    title_link = article_element.find('h3').find('a') if article_element.find('h3') else None
    if not title_link:
        print("未找到标题链接")
        return None

    title = title_link.get_text(strip=True)
    article_url = title_link.get('href', '')

    meta_p = article_element.find('p', class_='meta')
    date = ''
    author = ''

    if meta_p:
        # 首先尝试提取日期部分（在 | 之前的文本）
        meta_text = meta_p.get_text(strip=True)
        print(f"Meta文本: {meta_text}")
        
        if '|' in meta_text:
            parts = meta_text.split('|')
            if len(parts) >= 2:
                date = parts[0].strip()
                
                # 尝试从<a>标签中提取作者名
                author_link = meta_p.find('a')
                if author_link:
                    author = author_link.get_text(strip=True)
                    print(f"从<a>标签提取作者: {author}")
                else:
                    # 如果没有<a>标签，从文本中提取
                    author_part = parts[1].strip()
                    if author_part.startswith('By '):
                        author = author_part[3:].strip()
                    print(f"从文本提取作者: {author}")
        else:
            date = meta_text

    excerpt = ''
    excerpt_p = article_element.find('p', class_='listOnly')
    if excerpt_p:
        excerpt = excerpt_p.get_text(strip=True)

    result = {
        'title': title,
        'date': date,
        'author': author,
        'excerpt': excerpt,
        'url': article_url
    }
    
    print("提取结果:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    return result

if __name__ == "__main__":
    print("测试作者和日期提取功能")
    print("-" * 40)
    test_meta_extraction()
